# screen-visual

基于 Vite 的可视化大屏项目，包含多种数据可视化页面，适用于电力监控、能耗分析等场景。

## 目录结构

```
├── package.json
├── pnpm-lock.yaml
├── pnpm-workspace.yaml
├── vite.config.js
├── pages/
│   ├── about/
│   │   └── index.html
│   └── balance-monitoring/
│       ├── index.html
│       ├── main.js
│       ├── src/
│       │   ├── assets/
│       │   │   ├── css/
│       │   │   │   └── style.css
│       │   │   └── images/
│       │   │       ├── BalanceBg.webp
│       │   │       ├── bg.webp
│       │   │       ├── ChartBg.webp
│       │   │       ├── CurrentCost.webp
│       │   │       ├── RealTimeACE.webp
│       │   │       ├── SectionBg.webp
│       │   │       ├── SolarBg.webp
│       │   │       ├── Title.webp
│       │   │       └── WindBg.webp
│       │   ├── js/
│       │   │   ├── data.js
│       │   │   ├── components/
│       │   │   │   ├── BalanceChart.js
│       │   │   │   ├── CPSChart.js
│       │   │   │   ├── InfoDisplay.js
│       │   │   │   ├── SectionTable.js
│       │   │   │   ├── SolarChart.js
│       │   │   │   └── WindChart.js
│       │   │   └── config/
│       │   │       └── ChartConfig.js
│       │   └── test/
│       │       ├── index.html
│       │       └── test.png
```

## 主要依赖

- [Vite](https://vitejs.dev/)：前端构建工具
- [ECharts](https://echarts.apache.org/)：可视化图表库
- [D3.js](https://d3js.org/)：数据驱动文档操作库
- [dayjs](https://day.js.org/)：日期处理库

## 快速开始

### 安装依赖

```sh
pnpm install
```

### 启动开发服务器

```sh
pnpm dev
```

### 构建生产包

```sh
pnpm build
```

### 预览生产包

```sh
pnpm preview
```

## 说明

- 所有页面和组件均位于 `pages/` 目录下。
- 静态资源（图片、样式）位于 `assets/` 文件夹。
- 主要业务逻辑和可视化组件在 `js/components/` 下。


